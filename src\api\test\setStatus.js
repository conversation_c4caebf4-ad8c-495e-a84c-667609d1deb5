const Joi = require('joi');
const TestServices = require('../../services/TestServices');

module.exports = () => {
    return async (req, res, next) => {
        const schema = Joi.object().keys({
        });
        await schema.validateAsync(req.body)
            .then(param => {
                let services = new TestServices();

                services
                    .setStatus()
                    .then(result => { res.status(result.status).json(result.data) })
                    .catch(result => { res.status(result.status).json(result.data) });
            })
            .catch(next);
    };
};
