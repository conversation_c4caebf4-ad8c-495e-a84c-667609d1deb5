<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-tachometer-alt"></i> System Dashboard</h2>
        <p class="text-muted">Real-time overview of system status and performance metrics</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{serverStatus.status}}</h4>
                        <p class="card-text">Server Status</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-server fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{serverStatus.uptime}}s</h4>
                        <p class="card-text">Uptime</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{serverStatus.memoryUsage}}MB</h4>
                        <p class="card-text">Memory Usage</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-memory fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{apiEndpoints.length}}</h4>
                        <p class="card-text">API Endpoints</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-link fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Environment Details</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Environment:</strong></td>
                                <td><span class="badge bg-primary">{{serverStatus.environment}}</span></td>
                            </tr>
                            <tr>
                                <td><strong>Node Version:</strong></td>
                                <td>{{serverStatus.nodeVersion}}</td>
                            </tr>
                            <tr>
                                <td><strong>Platform:</strong></td>
                                <td>{{serverStatus.platform}}</td>
                            </tr>
                            <tr>
                                <td><strong>Architecture:</strong></td>
                                <td>{{serverStatus.arch}}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Memory Details</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>RSS:</strong></td>
                                <td>{{serverStatus.memory.rss}} MB</td>
                            </tr>
                            <tr>
                                <td><strong>Heap Used:</strong></td>
                                <td>{{serverStatus.memory.heapUsed}} MB</td>
                            </tr>
                            <tr>
                                <td><strong>Heap Total:</strong></td>
                                <td>{{serverStatus.memory.heapTotal}} MB</td>
                            </tr>
                            <tr>
                                <td><strong>External:</strong></td>
                                <td>{{serverStatus.memory.external}} MB</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/test" class="btn btn-primary">
                        <i class="fas fa-flask"></i> Test API
                    </a>
                    <a href="/status" class="btn btn-success">
                        <i class="fas fa-heartbeat"></i> Health Check
                    </a>
                    <button class="btn btn-info" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> Refresh Data
                    </button>
                    <a href="/api/test/health" class="btn btn-outline-secondary" target="_blank">
                        <i class="fas fa-external-link-alt"></i> API Health
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clock"></i> Last Updated
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-0">{{lastUpdated}}</p>
                <small class="text-muted">Auto-refresh every 30 seconds</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Available API Endpoints
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Method</th>
                                <th>Endpoint</th>
                                <th>Description</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{#each apiEndpoints}}
                            <tr>
                                <td><span class="badge bg-{{methodColor}}">{{method}}</span></td>
                                <td><code>{{endpoint}}</code></td>
                                <td>{{description}}</td>
                                <td>
                                    <a href="{{endpoint}}" class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-external-link-alt"></i> Test
                                    </a>
                                </td>
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshDashboard() {
    location.reload();
}

// Auto-refresh dashboard every 30 seconds
setTimeout(function() {
    refreshDashboard();
}, 30000);
</script>
