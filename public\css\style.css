/* Custom styles for Jaya Prima Frontend */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: 600;
}

.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.json-display {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    white-space: pre-wrap;
    max-height: 400px;
    overflow-y: auto;
    line-height: 1.4;
}

.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.online {
    background-color: #28a745;
}

.status-indicator.offline {
    background-color: #dc3545;
}

.status-indicator.warning {
    background-color: #ffc107;
}

.timeline-item {
    position: relative;
    padding-left: 20px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #007bff;
}

.progress {
    height: 8px;
}

.btn {
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

.footer {
    margin-top: auto;
}

/* Loading spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .json-display {
        font-size: 0.75rem;
        max-height: 300px;
    }
}

/* Custom badge colors */
.badge.bg-online {
    background-color: #28a745 !important;
}

.badge.bg-offline {
    background-color: #dc3545 !important;
}

/* Form enhancements */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Table enhancements */
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

/* Custom scrollbar for json-display */
.json-display::-webkit-scrollbar {
    width: 8px;
}

.json-display::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.json-display::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.json-display::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
