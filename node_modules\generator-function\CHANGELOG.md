# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v2.0.0](https://github.com/TimothyGu/generator-function/compare/v1.0.0...v2.0.0) - 2025-09-29

### Commits

- Initial reimplementation, tests, readme, types [`e5940de`](https://github.com/TimothyGu/generator-function/commit/e5940de4328fc70ad27aa4bab4245f6c9ce62a44)
- [meta] remove unused files [`460bbe9`](https://github.com/TimothyGu/generator-function/commit/460bbe9cc6c89f9fdab05a001dc924308124613b)
- [Dev Deps] add missing peer dep [`b873c0d`](https://github.com/TimothyGu/generator-function/commit/b873c0dad8f38b08e2acc4bd5204a90dde0fe051)
- [meta] fix FUNDING.yml [`9ae9d43`](https://github.com/TimothyGu/generator-function/commit/9ae9d432a0c06d698e1aeb20b1161ca8a08b3cbb)

## v1.0.0 - 2015-10-10

### Commits

- Initial commit [`176e0cd`](https://github.com/TimothyGu/generator-function/commit/176e0cd3a5ebb004aa666c6eecbe5a968efbddf9)
