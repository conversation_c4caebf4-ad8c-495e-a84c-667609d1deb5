class TestServices {

    constructor() {
    }

    test(data) {
        return new Promise(async (resolve, reject) => {
            const data = {
                    status: true,
                    messages: "Data Berhasil Ditambahkan",
                    data: exam
                }
                resolve({ data: data, status: httpCodes.OK });D
        });
    }
}

module.exports = TestServices;