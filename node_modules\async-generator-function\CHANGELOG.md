# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## v1.0.0 - 2025-02-14

### Commits

- Initial implementation, tests, readme, types [`5a239d4`](https://github.com/ljharb/async-generator-function/commit/5a239d463b2cbc4658d0dd3baeaaeae9a137e6a0)
- Initial commit [`603f1d9`](https://github.com/ljharb/async-generator-function/commit/603f1d9686f22235a61092abee703674f6fd3ffd)
- npm init [`ba54949`](https://github.com/ljharb/async-generator-function/commit/ba549496656d1cd545813769fd9e06020c1edd41)
- Only apps should have lockfiles [`b66a110`](https://github.com/ljharb/async-generator-function/commit/b66a11020ef902d96a31c523cfb2723c4e2ca998)
